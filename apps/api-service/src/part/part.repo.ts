import { Injectable } from "@nestjs/common";

import { manufacturer, part, Prisma } from "../generated/client";
import { PrismaService } from "../prisma.service";
import { CreatePartInput, UpdatePartInput } from "./types";

@Injectable()
export class PartRepository {
  constructor(private prisma: PrismaService) {}

  async findById(
    id: number,
    includeMpnSku: boolean = false,
  ): Promise<part | null> {
    return this.prisma.part.findUnique({
      include: this.getIncludeOptions(includeMpnSku),
      where: { id },
    });
  }

  async findMany(params: {
    skip?: number;
    take?: number;
    where?: Prisma.partWhereInput;
    orderBy?: Prisma.partOrderByWithRelationInput;
    includeMpnSku?: boolean;
  }): Promise<part[]> {
    const { skip, take, where, orderBy, includeMpnSku = false } = params;

    return this.prisma.part.findMany({
      include: this.getIncludeOptions(includeMpnSku),
      orderBy,
      skip,
      take,
      where,
    });
  }

  async findManyByKeyword(
    keyword: string,
    limit: number,
    includeMpnSke: boolean,
  ) {
    return this.prisma.part.findMany({
      include: this.getIncludeOptions(includeMpnSke),
      take: limit,
      where: {
        OR: [
          {
            mpn: {
              contains: keyword,
              mode: "insensitive",
            },
          },
          {
            description: {
              contains: keyword,
              mode: "insensitive",
            },
          },
        ],
      },
    });
  }

  async findManyByKeywordWithSupplies(
    keyword: string,
    limit: number,
    sellerKeys?: string[],
  ) {
    return this.prisma.part.findMany({
      include: {
        manufacturer: true,
        supplies: {
          include: {
            seller: true,
          },
          where: sellerKeys
            ? {
                seller: {
                  key: {
                    in: sellerKeys,
                  },
                },
              }
            : undefined,
        },
      },
      take: limit,
      where: {
        OR: [
          {
            mpn: {
              contains: keyword,
              mode: "insensitive",
            },
          },
          {
            description: {
              contains: keyword,
              mode: "insensitive",
            },
          },
        ],
      },
    });
  }

  async findFirst(params: {
    where?: Prisma.partWhereInput;
    includeMpnSku?: boolean;
  }): Promise<part | null> {
    const { where, includeMpnSku = false } = params;

    return this.prisma.part.findFirst({
      include: this.getIncludeOptions(includeMpnSku),
      where,
    });
  }

  async create(data: CreatePartInput): Promise<part> {
    return this.prisma.part.create({
      data,
      include: {
        manufacturer: true,
      },
    });
  }

  async update(data: UpdatePartInput): Promise<part> {
    const { id, ...updateData } = data;
    return this.prisma.part.update({
      data: updateData,
      include: {
        manufacturer: true,
      },
      where: { id },
    });
  }

  async delete(id: number): Promise<part> {
    return this.prisma.part.delete({
      where: { id },
    });
  }

  async count(where?: Prisma.partWhereInput): Promise<number> {
    return this.prisma.part.count({ where });
  }

  async findByMpnAndManufacturer(
    mpn: string,
    manufacturerName?: string,
  ): Promise<(part & { manufacturer: manufacturer }) | null> {
    return this.prisma.part.findFirst({
      include: {
        manufacturer: true,
      },
      where: {
        mpn: {
          equals: mpn,
          mode: "insensitive",
        },
        ...(manufacturerName
          ? {
              manufacturer: {
                name: {
                  equals: manufacturerName,
                  mode: "insensitive",
                },
              },
            }
          : {}),
      },
    });
  }

  async findByZ2PartId(
    z2PartId: number,
  ): Promise<(part & { manufacturer: manufacturer }) | null> {
    return this.prisma.part.findFirst({
      include: {
        manufacturer: true,
      },
      where: {
        z2_part_id: z2PartId,
      },
    });
  }

  async findOrCreateManufacturer(companyName: string): Promise<manufacturer> {
    const foundManufacturer = await this.prisma.manufacturer.findFirst({
      where: {
        name: {
          equals: companyName,
          mode: "insensitive",
        },
      },
    });

    if (foundManufacturer) return foundManufacturer;

    return this.prisma.manufacturer.create({
      data: { name: companyName },
    });
  }

  async findManyByMpns(mpns: string[]): Promise<(part & { manufacturer: manufacturer })[]> {
    return this.prisma.part.findMany({
      include: {
        manufacturer: true,
      },
      where: {
        mpn: {
          in: mpns,
          mode: "insensitive",
        },
      },
    });
  }

  async findManyByMpnAndManufacturerBatch(
    whereConditions: Array<{ mpn: string; manufacturer: { name?: string } }>
  ): Promise<(part & { manufacturer: manufacturer })[]> {
    return this.prisma.part.findMany({
      include: {
        manufacturer: true,
      },
      where: {
        OR: whereConditions.map(condition => ({
          mpn: {
            equals: condition.mpn,
            mode: "insensitive",
          },
          manufacturer: condition.manufacturer.name ? {
            name: {
              equals: condition.manufacturer.name,
              mode: "insensitive",
            },
          } : undefined,
        })),
      },
    });
  }

  private getIncludeOptions(includeMpnSku: boolean) {
    return {
      manufacturer: true,
      ...(includeMpnSku
        ? {
            mpn_sku: {
              include: {
                seller: true,
              },
            },
          }
        : {}),
    };
  }
}

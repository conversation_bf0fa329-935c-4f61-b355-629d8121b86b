import { Injectable, Logger } from "@nestjs/common";

import { MatchedStatus } from "../bom/types";
import {
  PartMapping,
  SellerId,
  sellerIdFromId,
  SkuBySeller,
} from "../common/types";
import { manufacturer, part } from "../generated/client";
import { ManufacturerNormalizationService } from "../manufacturer/manufacturer-normalization.service";
import { Z2DataService } from "../z2data/z2data.service";
import {
  Z2CrossItem,
  Z2PartSearchResult,
  Z2ValidationResult,
} from "../z2data/z2data.types";
import { PartRepository } from "./part.repo";
import { PartAlternativeRepo } from "./part-alternative.repo";
import { CreatePartInput, PartAlternativeResponse } from "./types";
import { UpdatePartInput } from "./types/inputs";

@Injectable()
export class PartService {
  private readonly defaultLimit = 5;
  private readonly maxLimit = 100;
  private readonly logger = new Logger(PartService.name);

  constructor(
    private partRepo: PartRepository,
    private z2dataService: Z2DataService,
    private partAlternativeRepo: PartAlternativeRepo,
    private manufacturerNormalizationService: ManufacturerNormalizationService,
  ) {}

  private async create(createPartInput: CreatePartInput): Promise<part> {
    return this.partRepo.create(createPartInput);
  }

  async findOne(id: number): Promise<part | null> {
    return this.partRepo.findById(id);
  }

  async findByKeyword(
    keyword: string,
    limit: number = this.defaultLimit,
    includeMpnSku: boolean = false,
    forceUpdate: boolean = false,
  ) {
    if (forceUpdate) {
      this.logger.debug(`Force update requested for keyword: ${keyword}`);
      const z2Parts = await this.z2dataService.searchParts(keyword);
      if (z2Parts.length > 0) {
        await this.syncZ2PartsToDatabase(z2Parts);
      }
    }

    return this.partRepo.findManyByKeyword(
      keyword,
      Math.min(limit, this.maxLimit),
      includeMpnSku,
    );
  }

  private async syncZ2PartsToDatabase(
    parts: Z2PartSearchResult[],
  ): Promise<void> {
    for (const part of parts) {
      try {
        const normalizedManufacturerName =
          await this.manufacturerNormalizationService.normalizeManufacturer(
            part.Manufacturer,
          );

        this.logger.debug(
          `Normalized manufacturer "${part.Manufacturer}" to "${normalizedManufacturerName}"`,
        );

        const manufacturer = await this.partRepo.findOrCreateManufacturer(
          normalizedManufacturerName,
        );

        const existingPart = await this.partRepo.findByMpnAndManufacturer(
          part.MPN,
          normalizedManufacturerName,
        );

        if (!existingPart) {
          await this.partRepo.create({
            description: part.Description,
            manufacture_id: manufacturer.id,
            mpn: part.MPN,
            pl_name: part.ProductType,
            z2_part_id: part.PartID,
          });
          this.logger.debug(
            `Created new part: ${part.MPN} from manufacturer ${normalizedManufacturerName}`,
          );
        } else {
          await this.partRepo.update({
            id: existingPart.id,
            description: part.Description,
            pl_name: part.ProductType,
            z2_part_id: part.PartID,
          });
          this.logger.debug(
            `Updated existing part: ${part.MPN} from manufacturer ${normalizedManufacturerName}`,
          );
        }
      } catch (error) {
        this.logger.error(
          `Failed to sync part ${part.MPN} to database: ${
            error instanceof Error ? error.message : "Unknown error"
          }`,
        );
      }
    }
  }

  async findPartMappingByKeyword(
    keyword: string,
    limit: number = this.defaultLimit,
    sellerKeys?: string[],
  ): Promise<PartMapping[]> {
    const matchingParts = await this.partRepo.findManyByKeywordWithSupplies(
      keyword,
      Math.min(limit, this.maxLimit),
      sellerKeys,
    );

    return matchingParts.map((value) => {
      const skuMap = new Map<SellerId, string[]>();
      value.supplies.forEach((supply) => {
        const sellerId = sellerIdFromId(supply.seller_id);
        const currentSkus = skuMap.get(sellerId) || [];
        skuMap.set(sellerId, [...currentSkus, supply.sku]);
      });

      const skuBySellers: SkuBySeller[] = [];
      skuMap.forEach((skusList, sellerId) => {
        skuBySellers.push({
          sellerId: sellerId.toString(),
          skus: skusList,
        });
      });

      return {
        mpn: value.mpn,
        skus: skuBySellers,
      };
    });
  }

  async findByMpnAndManufacturer(
    mpn: string,
    manufacturerName?: string,
  ): Promise<(part & { manufacturer: manufacturer }) | null> {
    // Normalize the manufacturer name if provided
    const normalizedManufacturerName = manufacturerName
      ? await this.manufacturerNormalizationService.normalizeManufacturer(
          manufacturerName,
        )
      : undefined;

    this.logger.debug(
      `Finding part by MPN: ${mpn} and manufacturer: ${normalizedManufacturerName || "NA"}`,
    );
    return this.partRepo.findByMpnAndManufacturer(
      mpn,
      normalizedManufacturerName,
    );
  }

  public async findOrCreatePartBy(z2Part: Z2ValidationResult): Promise<part> {
    const foundPart = await this.partRepo.findByZ2PartId(
      z2Part.z2PartData.partID,
    );
    if (foundPart) {
      // Ensure existing part has fresh Z2Data
      await this.ensurePartHasFreshZ2Data(foundPart.id);
      return foundPart;
    }

    const normalizedManufacturerName =
      await this.manufacturerNormalizationService.normalizeManufacturer(
        z2Part.z2PartData.companyName,
      );

    const manufacturer = await this.partRepo.findOrCreateManufacturer(
      normalizedManufacturerName,
    );

    const newPart = await this.create({
      manufacture_id: manufacturer.id,
      mpn: z2Part.z2PartData.partNumber,
      z2_part_id: z2Part.z2PartData.partID,
    } as CreatePartInput);

    // For newly created parts, immediately update with Z2Data
    await this.ensurePartHasFreshZ2Data(newPart.id);

    return newPart;
  }

  private readonly Z2MATCHED_STATUS_EXACT: string = "Exact";
  private readonly Z2MATCHED_STATUS_SIMILAR: string = "Similar";

  private async processAlternativeItems(
    alternativeItems: Z2CrossItem[],
  ): Promise<Array<Z2CrossItem & { localPartId: number }>> {
    const processedItems: Array<Z2CrossItem & { localPartId: number }> = [];

    for (const item of alternativeItems) {
      this.logger.debug(
        `Processing alternative item: ${item.partNumber} from ${item.companyName}`,
      );

      // Search for the part locally first
      const localPart =
        await this.partAlternativeRepo.findAlternativePartLocally(
          item.partNumber,
          item.companyName,
        );

      if (localPart) {
        this.logger.debug(
          `Found alternative part locally: ${item.partNumber} (ID: ${localPart.id})`,
        );
        processedItems.push({
          ...item,
          localPartId: localPart.id,
        });
      } else {
        this.logger.debug(
          `Alternative part not found locally: ${item.partNumber}, searching by keyword`,
        );

        // Search by keyword and verify exact match
        const keywordResults = await this.findByKeyword(item.partNumber, 10);
        const exactMatch = keywordResults.find(
          (part) =>
            part.mpn.toLowerCase() === item.partNumber.toLowerCase() &&
            part.manufacturer?.name.toLowerCase() ===
              item.companyName.toLowerCase(),
        );

        if (exactMatch) {
          this.logger.debug(
            `Found exact match by keyword search: ${item.partNumber} (ID: ${exactMatch.id})`,
          );
          processedItems.push({
            ...item,
            localPartId: exactMatch.id,
          });
        } else {
          this.logger.debug(
            `No exact match found, creating part from Z2Data: ${item.partNumber}`,
          );

          // Try to find or create the part using Z2Data validation
          const [foundOrCreatedPart] = await this.findPartByMpnAndManufacturer(
            item.partNumber,
            item.companyName,
          );

          if (foundOrCreatedPart) {
            this.logger.debug(
              `Created alternative part from Z2Data: ${item.partNumber} (ID: ${foundOrCreatedPart.id})`,
            );
            processedItems.push({
              ...item,
              localPartId: foundOrCreatedPart.id,
            });
          } else {
            this.logger.warn(
              `Could not create alternative part from Z2Data: ${item.partNumber}, skipping this alternative`,
            );
            // Skip this item since we can't create it and part_id is now required
            continue;
          }
        }
      }
    }

    return processedItems;
  }

  public async findPartByMpnAndManufacturer(
    mpn: string,
    manufacturer_name?: string,
  ): Promise<[part | null, MatchedStatus, string]> {
    if (manufacturer_name) {
      const foundPart = await this.findByMpnAndManufacturer(
        mpn,
        manufacturer_name,
      );
      if (foundPart) {
        return [
          foundPart,
          MatchedStatus.Matched,
          "Exact match by MPN and manufacturer",
        ];
      }
    }

    const z2ValidationResponse = await this.z2dataService.validateParts({
      rows: [
        {
          man: manufacturer_name || "",
          mpn,
          rowNumber: 1,
        },
      ],
    });

    if (z2ValidationResponse && z2ValidationResponse.statusCode === 200) {
      const z2Part = z2ValidationResponse.results.find(
        (row) =>
          row.rowNumber === 1 &&
          (row.matchStatus === this.Z2MATCHED_STATUS_EXACT ||
            row.matchStatus === this.Z2MATCHED_STATUS_SIMILAR),
      );
      if (z2Part) {
        const createdPart = await this.findOrCreatePartBy(z2Part);
        const matchedStatus =
          z2Part.matchStatus === this.Z2MATCHED_STATUS_EXACT
            ? MatchedStatus.Matched
            : MatchedStatus.SemiMatched;
        return [createdPart, matchedStatus, z2Part.matchReason];
      }
    }

    return [null, MatchedStatus.NotMatched, "No match found"];
  }

  public async batchFindPartsByMpnAndManufacturer(
    partRequests: Array<{ mpn: string; manufacturer_name?: string; originalIndex: number }>,
  ): Promise<Array<{
    part: part | null;
    matchedStatus: MatchedStatus;
    matchReason: string;
    originalIndex: number;
  }>> {
    this.logger.debug(`Batch processing ${partRequests.length} part requests`);

    // Step 1: Try to find parts locally first (using individual lookups to avoid long WHERE clauses)
    const localResults = new Map<number, { part: part; matchedStatus: MatchedStatus; matchReason: string }>();
    const unmatchedRequests: Array<{ mpn: string; manufacturer_name?: string; originalIndex: number }> = [];

    for (const request of partRequests) {
      // Try local lookup first using existing individual method
      const localPart = request.manufacturer_name
        ? await this.findByMpnAndManufacturer(request.mpn, request.manufacturer_name)
        : null;

      if (localPart) {
        localResults.set(request.originalIndex, {
          part: localPart,
          matchedStatus: MatchedStatus.Matched,
          matchReason: "Exact match by MPN and manufacturer",
        });
      } else {
        unmatchedRequests.push(request);
      }
    }

    this.logger.debug(`Found ${localResults.size} parts locally, ${unmatchedRequests.length} need Z2Data validation`);

    // Step 2: Batch validate unmatched parts with Z2Data (in chunks of 500)
    const z2Results = unmatchedRequests.length > 0
      ? await this.batchValidateWithZ2Data(unmatchedRequests)
      : new Map();

    // Step 3: Map results back to original order
    return this.mapBatchResultsToOriginalOrderSimple(localResults, z2Results, partRequests);
  }



  private async batchValidateWithZ2Data(
    unmatchedRequests: Array<{ mpn: string; manufacturer_name?: string; originalIndex: number }>
  ): Promise<Map<number, { part: part; matchedStatus: MatchedStatus; matchReason: string }>> {
    const results = new Map();

    // Process in chunks of 500 (Z2Data API limit)
    const chunks = this.chunkPartRequests(unmatchedRequests, 500);

    for (const chunk of chunks) {
      try {
        const chunkResults = await this.processSingleBatch(chunk);
        chunkResults.forEach(result => {
          if (result.part) {
            results.set(result.originalIndex, {
              part: result.part,
              matchedStatus: result.matchedStatus,
              matchReason: result.matchReason,
            });
          }
        });
      } catch (error) {
        this.logger.error(`Failed to process batch chunk: ${error instanceof Error ? error.message : 'Unknown error'}`);
        // Continue with next chunk rather than failing entire batch
      }
    }

    return results;
  }

  private chunkPartRequests<T>(
    requests: T[],
    chunkSize: number = 500
  ): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < requests.length; i += chunkSize) {
      chunks.push(requests.slice(i, i + chunkSize));
    }
    return chunks;
  }

  private async processSingleBatch(
    batch: Array<{ mpn: string; manufacturer_name?: string; originalIndex: number }>
  ): Promise<Array<{
    part: part | null;
    matchedStatus: MatchedStatus;
    matchReason: string;
    originalIndex: number;
  }>> {
    this.logger.debug(`Processing Z2Data batch of ${batch.length} parts`);

    // Prepare Z2Data validation request
    const z2ValidationRequest = {
      rows: batch.map((request, index) => ({
        man: request.manufacturer_name || "",
        mpn: request.mpn,
        rowNumber: index + 1, // Z2Data expects 1-based row numbers
      })),
    };

    const z2ValidationResponse = await this.z2dataService.validateParts(z2ValidationRequest);

    if (!z2ValidationResponse || z2ValidationResponse.statusCode !== 200) {
      this.logger.warn(`Z2Data validation failed for batch of ${batch.length} parts`);
      return batch.map(request => ({
        part: null,
        matchedStatus: MatchedStatus.NotMatched,
        matchReason: "Z2Data validation failed",
        originalIndex: request.originalIndex,
      }));
    }

    // Process each result
    const results: Array<{
      part: part | null;
      matchedStatus: MatchedStatus;
      matchReason: string;
      originalIndex: number;
    }> = [];

    for (let i = 0; i < batch.length; i++) {
      const request = batch[i];
      const z2Result = z2ValidationResponse.results.find(
        result => result.rowNumber === i + 1 &&
        (result.matchStatus === this.Z2MATCHED_STATUS_EXACT ||
         result.matchStatus === this.Z2MATCHED_STATUS_SIMILAR)
      );

      if (z2Result) {
        try {
          const createdPart = await this.findOrCreatePartBy(z2Result);
          const matchedStatus = z2Result.matchStatus === this.Z2MATCHED_STATUS_EXACT
            ? MatchedStatus.Matched
            : MatchedStatus.SemiMatched;

          results.push({
            part: createdPart,
            matchedStatus,
            matchReason: z2Result.matchReason,
            originalIndex: request.originalIndex,
          });
        } catch (error) {
          this.logger.error(`Failed to create part from Z2Data result: ${error instanceof Error ? error.message : 'Unknown error'}`);
          results.push({
            part: null,
            matchedStatus: MatchedStatus.NotMatched,
            matchReason: "Failed to create part from Z2Data",
            originalIndex: request.originalIndex,
          });
        }
      } else {
        results.push({
          part: null,
          matchedStatus: MatchedStatus.NotMatched,
          matchReason: "No match found in Z2Data",
          originalIndex: request.originalIndex,
        });
      }
    }

    return results;
  }

  private mapBatchResultsToOriginalOrderSimple(
    localResults: Map<number, { part: part; matchedStatus: MatchedStatus; matchReason: string }>,
    z2Results: Map<number, { part: part; matchedStatus: MatchedStatus; matchReason: string }>,
    originalRequests: Array<{ mpn: string; manufacturer_name?: string; originalIndex: number }>
  ): Array<{
    part: part | null;
    matchedStatus: MatchedStatus;
    matchReason: string;
    originalIndex: number;
  }> {
    return originalRequests.map(request => {
      // First check local results
      const localResult = localResults.get(request.originalIndex);
      if (localResult) {
        return {
          part: localResult.part,
          matchedStatus: localResult.matchedStatus,
          matchReason: localResult.matchReason,
          originalIndex: request.originalIndex,
        };
      }

      // Then check Z2Data results
      const z2Result = z2Results.get(request.originalIndex);
      if (z2Result) {
        return {
          part: z2Result.part,
          matchedStatus: z2Result.matchedStatus,
          matchReason: z2Result.matchReason,
          originalIndex: request.originalIndex,
        };
      }

      // No match found
      return {
        part: null,
        matchedStatus: MatchedStatus.NotMatched,
        matchReason: "No match found",
        originalIndex: request.originalIndex,
      };
    });
  }

  async getPartAlternatives(
    partId: number,
  ): Promise<PartAlternativeResponse | null> {
    this.logger.debug(`Getting alternatives for part ID: ${partId}`);

    const part = await this.findOne(partId);
    if (!part || !part.z2_part_id) {
      this.logger.warn(
        `Part with ID ${partId} not found or missing z2_part_id`,
      );
      return null;
    }

    try {
      const cachedAlternatives =
        await this.partAlternativeRepo.findCachedAlternatives(partId);
      if (cachedAlternatives) {
        this.logger.debug(
          `Returning cached alternatives for part ID ${partId}`,
        );
        return cachedAlternatives;
      }
    } catch (error) {
      this.logger.warn(
        `Failed to fetch cached alternatives for part ID ${partId}, falling back to API: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      );
    }

    this.logger.debug(`Cache miss for part ID ${partId}, calling Z2Data API`);
    const z2Response = await this.z2dataService.getCrossDataByPartId(
      part.z2_part_id,
    );
    if (!z2Response || z2Response.statusCode !== 200) {
      this.logger.warn(
        `Failed to get cross data from Z2Data for part ID ${partId}`,
      );
      return null;
    }

    // Process alternative items to find/create local part references
    this.logger.debug(
      `Processing ${z2Response.results.crossesDetails.crosses.length} alternative items for local part mapping`,
    );
    const processedAlternativeItems = await this.processAlternativeItems(
      z2Response.results.crossesDetails.crosses,
    );

    try {
      await this.partAlternativeRepo.upsertAlternatives(
        partId,
        part.z2_part_id,
        z2Response,
        processedAlternativeItems,
      );
      this.logger.debug(
        `Successfully cached alternatives for part ID ${partId} with local part references`,
      );
    } catch (error) {
      this.logger.warn(
        `Failed to cache alternatives for part ID ${partId}: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      );
    }

    // Fetch part data for each alternative to include in response
    const alternativesWithPartData = await Promise.all(
      processedAlternativeItems.map(async (cross) => {
        const partData = await this.partRepo.findById(cross.localPartId);
        return {
          companyName: cross.companyName,
          crossComment: cross.crossComment,
          crossType: cross.crossType,
          dataSheet: cross.dataSheet,
          mpn: cross.partNumber,
          package: cross.package,
          partDescription: cross.partDescription,
          partLifecycle: cross.partLifecycle,
          roHsFlag: cross.roHsFlag,
          part: partData || undefined,
        };
      }),
    );

    return {
      alternatives: alternativesWithPartData,
      companyName: z2Response.results.companyName,
      dataSheet: z2Response.results.dataSheet,
      mpn: z2Response.results.partNumber,
      numFound: z2Response.results.numFound,
      pageNumber: z2Response.results.pageNumber,
      partLifecycle: z2Response.results.partLifecycle,
      roHsFlag: z2Response.results.roHsFlag,
      totalCrossesFound: z2Response.results.crossesDetails.Total_Crosses_Found,
    };
  }

  async updatePartFromZ2Data(partId: number): Promise<part | null> {
    this.logger.debug(`Updating part ID ${partId} from Z2Data API`);

    const existingPart = await this.findOne(partId);
    if (!existingPart || !existingPart.z2_part_id) {
      this.logger.warn(
        `Part with ID ${partId} not found or missing z2_part_id, cannot update from Z2Data`,
      );
      return null;
    }

    try {
      const z2Response = await this.z2dataService.getPartDetailsById(
        existingPart.z2_part_id,
      );

      if (!z2Response || z2Response.statusCode !== 200) {
        this.logger.warn(
          `Failed to get part details from Z2Data for part ID ${partId} (Z2 Part ID: ${existingPart.z2_part_id})`,
        );
        return null;
      }

      const updateData: UpdatePartInput = {
        id: existingPart.id,
        last_z2data_update: new Date(),
      };

      if (z2Response.results?.MPNSummary?.Description) {
        updateData.description = z2Response.results.MPNSummary.Description;
      }

      if (z2Response.results?.MPNSummary?.ProductType) {
        updateData.pl_name = z2Response.results.MPNSummary.ProductType;
      }

      if (z2Response.results?.MPNSummary?.Category) {
        updateData.category = z2Response.results.MPNSummary.Category;
      }

      if (z2Response.results?.MPNSummary?.SubCategory) {
        updateData.subcategory0 = z2Response.results.MPNSummary.SubCategory;
      }

      if (z2Response.results?.MPNSummary?.FamilyName) {
        updateData.family_series = z2Response.results.MPNSummary.FamilyName;
      }

      if (z2Response.results?.Lifecycle?.LifecycleStatus) {
        updateData.lifecycle_status =
          z2Response.results.Lifecycle.LifecycleStatus;
      }

      if (z2Response.results?.ComplianceDetails?.RoHSStatus) {
        updateData.rohs_status =
          z2Response.results.ComplianceDetails.RoHSStatus;
      }

      // Market Availability Data
      if (z2Response.results?.MarketAvailabilitySummary?.MarketStatus) {
        updateData.market_status =
          z2Response.results.MarketAvailabilitySummary.MarketStatus;
      }

      if (z2Response.results?.MarketAvailabilitySummary?.NumberOfSeller) {
        updateData.number_of_seller =
          z2Response.results.MarketAvailabilitySummary.NumberOfSeller;
      }

      if (
        z2Response.results?.MarketAvailabilitySummary?.TotalQuantityAvailable
      ) {
        updateData.total_quantity_available =
          z2Response.results.MarketAvailabilitySummary.TotalQuantityAvailable;
      }

      if (z2Response.results?.MarketAvailabilitySummary?.LowestPrice) {
        updateData.lowest_price =
          z2Response.results.MarketAvailabilitySummary.LowestPrice;
      }

      if (z2Response.results?.MarketAvailabilitySummary?.MinLeadTime_weeks) {
        updateData.min_lead_time_weeks =
          z2Response.results.MarketAvailabilitySummary.MinLeadTime_weeks;
      }

      if (z2Response.results?.MarketAvailabilitySummary?.MaxLeadTime_weeks) {
        updateData.max_lead_time_weeks =
          z2Response.results.MarketAvailabilitySummary.MaxLeadTime_weeks;
      }

      const updatedPart = await this.partRepo.update(updateData);

      this.logger.debug(
        `Successfully updated part ID ${partId} from Z2Data API with lifecycle: ${updateData.lifecycle_status}, RoHS: ${updateData.rohs_status}, market status: ${updateData.market_status}`,
      );

      return updatedPart;
    } catch (error) {
      this.logger.error(
        `Failed to update part ID ${partId} from Z2Data API: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      );
      return null;
    }
  }

  async ensurePartHasFreshZ2Data(partId: number): Promise<part | null> {
    const part = await this.findOne(partId);
    if (!part || !part.z2_part_id) {
      this.logger.debug(
        `Part ${partId} not found or missing z2_part_id, skipping Z2Data update`,
      );
      return part;
    }

    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    const needsUpdate =
      !part.last_z2data_update ||
      part.last_z2data_update < oneWeekAgo ||
      !part.lifecycle_status ||
      !part.rohs_status;

    if (needsUpdate) {
      this.logger.debug(
        `Part ${partId} Z2Data is stale (last update: ${part.last_z2data_update}), updating from Z2Data API`,
      );
      return await this.updatePartFromZ2Data(partId);
    }

    this.logger.debug(
      `Part ${partId} Z2Data is fresh (last update: ${part.last_z2data_update}), no update needed`,
    );
    return part;
  }
}

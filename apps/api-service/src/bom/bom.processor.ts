import { BadRequestException, Injectable, Logger } from "@nestjs/common";
import csvParser from "csv-parser";
import { Readable } from "stream";
import * as XLSX from "xlsx";

import { MatchedStatus } from "./types";
import { ManufacturerNormalizationService } from "../manufacturer/manufacturer-normalization.service";
import { PartService } from "../part/part.service";
import { part } from "../generated/client";
import { BomService } from "./bom.service";
import type {
  CreateBomInput,
  ExcelParseOptions,
  FileType,
  ParsedBomItem,
} from "./types";

@Injectable()
export class BomProcessor {
  private readonly logger = new Logger(BomProcessor.name);
  private readonly MAX_ITEM_LIMIT = 5;

  constructor(
    private readonly bomService: BomService,
    private readonly manufacturerNormalizationService: ManufacturerNormalizationService,
    private readonly partService: PartService,
  ) {}

  private detectFileType(mimetype: string): FileType {
    if (
      mimetype === "text/csv" ||
      (mimetype === "application/vnd.ms-excel" && !mimetype.includes("sheet"))
    ) {
      return "csv";
    }
    return "excel";
  }

  private async parseExcel(
    buffer: Buffer,
    options: ExcelParseOptions = {},
  ): Promise<ParsedBomItem[]> {
    const { sheetIndex = 0, headerRow = 0, skipEmptyRows = true } = options;

    try {
      const workbook = XLSX.read(buffer, { type: "buffer" });
      const sheetNames = workbook.SheetNames;

      if (sheetIndex >= sheetNames.length) {
        throw new BadRequestException(
          `Sheet index ${sheetIndex} not found. File has ${sheetNames.length} sheets.`,
        );
      }

      const worksheet = workbook.Sheets[sheetNames[sheetIndex]];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, {
        defval: "",
        header: 1,
        raw: false,
      }) as string[][];

      if (jsonData.length <= headerRow) {
        throw new BadRequestException(
          "Excel file is empty or has no data rows",
        );
      }

      const headers = jsonData[headerRow].map((h) =>
        String(h).toLowerCase().trim(),
      );

      const mpnIndex = headers.findIndex(
        (h) =>
          h.includes("mpn") ||
          h.includes("part number") ||
          h.includes("part #"),
      );
      const manufacturerIndex = headers.findIndex(
        (h) =>
          h.includes("manufacturer") ||
          h.includes("mfr") ||
          h.includes("brand"),
      );
      const quantityIndex = headers.findIndex(
        (h) =>
          h.includes("qty") || h.includes("quantity") || h.includes("count"),
      );

      if (mpnIndex === -1) {
        throw new BadRequestException(
          "Could not find MPN column. Please ensure your Excel file has a column with 'MPN', 'Part Number' or 'Part #'",
        );
      }

      const results: ParsedBomItem[] = [];

      for (let i = headerRow + 1; i < jsonData.length; i++) {
        const row = jsonData[i];

        if (skipEmptyRows && (!row || row.every((cell) => !cell))) {
          continue;
        }

        const mpn = row[mpnIndex]?.toString().trim();

        if (!mpn) {
          this.logger.warn(`Row ${i + 1} missing MPN, skipping`);
          continue;
        }

        results.push({
          manufacturer_name:
            manufacturerIndex >= 0
              ? row[manufacturerIndex]?.toString().trim() || ""
              : "",
          mpn,
          quantity:
            quantityIndex >= 0
              ? row[quantityIndex]?.toString().trim() || "1"
              : "1",
        });
      }

      return results;
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to parse Excel file: ${error instanceof Error ? error.message : "Unknown error"}`,
      );
    }
  }

  private async parseCsv(buffer: Buffer): Promise<ParsedBomItem[]> {
    return new Promise((resolve, reject) => {
      const results: ParsedBomItem[] = [];
      const readableStream = Readable.from(buffer);

      readableStream
        .pipe(csvParser())
        .on("data", (data) => {
          if (!data.mpn) {
            this.logger.warn(`Row missing MPN: ${JSON.stringify(data)}`);
            return;
          }

          results.push({
            manufacturer_name: data.manufacturer_name || "",
            mpn: data.mpn,
            quantity: data.quantity || "1",
          });
        })
        .on("end", () => {
          resolve(results);
        })
        .on("error", (error) => {
          reject(error);
        });
    });
  }

  public async processAndCreateBom(
    buffer: Buffer,
    filename?: string,
    mimetype?: string,
  ): Promise<string[]> {
    const fileType = this.detectFileType(mimetype || "text/csv");

    let bomItems: ParsedBomItem[];
    if (fileType === "excel") {
      bomItems = await this.parseExcel(buffer);
      this.logger.log(`Parsed ${bomItems.length} BOM items from Excel file`);
    } else {
      bomItems = await this.parseCsv(buffer);
      this.logger.log(`Parsed ${bomItems.length} BOM items from CSV file`);
    }

    if (bomItems.length === 0) {
      throw new BadRequestException(
        `${fileType === "excel" ? "Excel" : "CSV"} file is empty or has invalid format`,
      );
    }

    const processedItems = await this.processBomItems(bomItems);

    if (processedItems.length === 0) {
      throw new BadRequestException("No valid parts found in CSV");
    }

    this.logger.log(`Processed ${processedItems.length} BOM items`);

    const bomName =
      (filename ?? `BOM from ${fileType === "excel" ? "Excel" : "CSV"}`) +
      " - " +
      new Date().toISOString();
    const createBomInput: CreateBomInput = { name: bomName };
    const messages: string[] = [];
    const bom = await this.bomService.createBom(createBomInput);

    if (processedItems.length > this.MAX_ITEM_LIMIT) {
      messages.push(
        `Due to processing limitation, Only the first ${this.MAX_ITEM_LIMIT} items will be processed.`,
      );
    }

    // Batch process all parts first
    const itemsWithParts = await this.batchProcessBomItems(processedItems);

    // Then batch create BOM items
    await this.bomService.batchCreateBomItems(
      bom.id,
      itemsWithParts,
      this.MAX_ITEM_LIMIT
    );

    return messages;
  }

  private async processBomItems(
    items: ParsedBomItem[],
  ): Promise<ParsedBomItem[]> {
    const processedItems: ParsedBomItem[] = [];

    const manufacturerNames = [
      ...new Set(items.map((item) => item.manufacturer_name).filter(Boolean)),
    ];
    const normalizedManufacturers =
      await this.manufacturerNormalizationService.batchNormalizeManufacturer(
        manufacturerNames,
      );

    for (const item of items) {
      const normalizedManufacturer = item.manufacturer_name
        ? normalizedManufacturers.get(item.manufacturer_name) ||
          item.manufacturer_name
        : item.manufacturer_name;

      processedItems.push({
        ...item,
        manufacturer_name: normalizedManufacturer,
      });
    }

    return processedItems;
  }

  private async batchProcessBomItems(
    bomItems: ParsedBomItem[]
  ): Promise<Array<{
    bomItem: ParsedBomItem;
    part: part | null;
    matchedStatus: MatchedStatus;
    matchReason: string;
    index: number;
  }>> {
    this.logger.debug(`Batch processing ${bomItems.length} BOM items for part matching`);

    // Prepare part requests with original index tracking
    const partRequests = bomItems.map((item, index) => ({
      mpn: item.mpn,
      manufacturer_name: item.manufacturer_name || undefined,
      originalIndex: index,
    }));

    // Batch find parts using the new PartService method
    const partResults = await this.partService.batchFindPartsByMpnAndManufacturer(partRequests);

    // Map results back to BOM items with their original order
    const itemsWithParts = bomItems.map((bomItem, index) => {
      const partResult = partResults.find(result => result.originalIndex === index);

      return {
        bomItem,
        part: partResult?.part || null,
        matchedStatus: partResult?.matchedStatus || MatchedStatus.NotMatched,
        matchReason: partResult?.matchReason || "No match found",
        index,
      };
    });

    const matchedCount = itemsWithParts.filter(item => item.part !== null).length;
    this.logger.debug(`Batch processing complete: ${matchedCount}/${bomItems.length} parts matched`);

    return itemsWithParts;
  }
}
